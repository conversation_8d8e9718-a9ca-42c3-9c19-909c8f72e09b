<div class="container-fluid">
  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center py-5">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div [class.content-blur]="isLoading">
    <!-- Page Header -->
    <div class="modern-card mb-4">
      <div class="modern-card-header">
        <div class="d-flex align-items-center">
          <div class="page-icon me-3">
            <i class="fas fa-exchange-alt"></i>
          </div>
          <div>
            <h4 class="mb-0">İşlem Yönetimi</h4>
            <small class="text-muted">Üye işlemlerini görüntüle ve yönet</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction Summary Cards -->
    <div class="row mb-4">
      <!-- Daily Total Transaction Card -->
      <div class="col-md-6 mb-3">
        <div class="modern-stats-card daily-transaction-card">
          <div class="modern-stats-icon">
            <i class="fas fa-calendar-day"></i>
          </div>
          <div class="modern-stats-info">
            <h3 class="modern-stats-value">{{ dailyTransactionTotal | number:'1.2-2':'tr' }} ₺</h3>
            <p class="modern-stats-label">Bugün Toplam İşlem Tutarı</p>
          </div>
        </div>
      </div>
      <!-- Monthly Total Transaction Card -->
      <div class="col-md-6 mb-3">
        <div class="modern-stats-card monthly-transaction-card">
          <div class="modern-stats-icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="modern-stats-info">
            <h3 class="modern-stats-value">{{ monthlyTransactionTotal | number:'1.2-2':'tr' }} ₺</h3>
            <p class="modern-stats-label">Bu Ay Toplam İşlem Tutarı</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="modern-card mb-4">
      <div class="modern-card-body">
        <div class="row g-3">
          <div class="col-lg-6 col-md-12">
            <label class="form-label">
              <i class="fas fa-search me-2"></i>Üye Ara
            </label>
            <div class="search-container">
              <input
                type="text"
                class="form-control"
                placeholder="Üye adı ile ara..."
                [formControl]="searchControl"
              />
              <i class="fas fa-search search-icon"></i>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <label class="form-label">
              <i class="fas fa-calendar me-2"></i>Başlangıç Tarihi
            </label>
            <input
              type="date"
              class="form-control"
              [(ngModel)]="startDate"
              (change)="onDateFilterChange()"
            />
          </div>
          <div class="col-lg-3 col-md-6">
            <label class="form-label">
              <i class="fas fa-calendar me-2"></i>Bitiş Tarihi
            </label>
            <div class="d-flex gap-2">
              <input
                type="date"
                class="form-control"
                [(ngModel)]="endDate"
                (change)="onDateFilterChange()"
              />
              <button
                *ngIf="startDate || endDate"
                class="btn btn-outline-secondary"
                (click)="clearDateFilter()"
                title="Tarih filtrelerini temizle">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction View Toggle -->
    <div class="modern-card mb-4">
      <div class="modern-card-body">
        <div class="btn-group w-100" role="group">
          <button
            type="button"
            class="btn btn-outline-warning"
            [class.active]="!showPaidTransactions"
            (click)="toggleTransactionView(false)">
            <i class="fas fa-clock me-2"></i>
            Ödenmemiş İşlemler
          </button>
          <button
            type="button"
            class="btn btn-outline-success"
            [class.active]="showPaidTransactions"
            (click)="toggleTransactionView(true)">
            <i class="fas fa-check-circle me-2"></i>
            Ödenmiş İşlemler
          </button>
        </div>
      </div>
    </div>

    <!-- Transaction List -->
    <div class="modern-card">
      <div class="modern-card-body">
        <!-- Empty State -->
        <div *ngIf="transactionsByMember.length === 0" class="text-center py-5">
          <div class="empty-state">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">
              {{ showPaidTransactions ? "Ödenmiş işlem bulunamadı" : "Ödenmemiş işlem bulunamadı" }}
            </h5>
            <p class="text-muted">Farklı filtreler deneyebilirsiniz.</p>
          </div>
        </div>

        <!-- Member Transaction Groups -->
        <div *ngFor="let member of transactionsByMember" class="member-transaction-group mb-4">
          <!-- Member Header -->
          <div class="modern-card member-card">
            <div class="modern-card-header">
              <div class="row align-items-center w-100">
                <!-- Sol Taraf: Avatar ve Üye Bilgileri -->
                <div class="col-lg-4 col-md-5 col-sm-6">
                  <div class="d-flex align-items-center">
                    <div class="member-avatar me-3">
                      <i class="fas fa-user"></i>
                    </div>
                    <div>
                      <h6 class="mb-1 fw-bold">{{ member.memberName }}</h6>
                      <small class="text-muted">
                        {{ member.transactions.length }} işlem •
                        {{ getMemberTotalAmount(member) | currency:'TRY':'symbol-narrow':'1.2-2' }}
                      </small>
                    </div>
                  </div>
                </div>

                <!-- Orta: Ürün Özeti ve Borç Bilgisi -->
                <div class="col-lg-4 col-md-3 col-sm-6" *ngIf="member.totalDebt > 0 && !showPaidTransactions">
                  <div class="text-center">
                    <!-- Product Summary Badges -->
                    <div class="d-flex flex-wrap justify-content-center gap-1 mb-2">
                      <span *ngFor="let product of member.productSummary | keyvalue"
                            class="modern-badge modern-badge-info">
                        {{ product.key }}: {{ product.value.quantity }}
                      </span>
                    </div>
                    <!-- Total Debt Display -->
                    <div>
                      <small class="text-muted d-block">Toplam Borç</small>
                      <strong class="text-danger fs-6">
                        {{ member.totalDebt | currency:'TRY':'symbol-narrow':'1.2-2' }}
                      </strong>
                    </div>
                  </div>
                </div>

                <!-- Sağ Taraf: Durum Badge'i ve Ödeme Butonu -->
                <div class="col-lg-4 col-md-4 col-12">
                  <div class="d-flex align-items-center justify-content-end gap-2">
                    <!-- Pay All Button - En Sağda -->
                    <button *ngIf="member.totalDebt > 0 && !showPaidTransactions"
                            class="btn btn-success btn-sm"
                            (click)="updateAllMemberPayments(member.memberId)"
                            title="Tüm borçları öde">
                      <i class="fas fa-money-bill-wave me-1"></i>
                      Tümünü Öde
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Transaction Table -->
            <div class="modern-card-body">
              <div class="table-responsive">
                <table class="modern-table">
                  <thead>
                    <tr>
                      <th><i class="fas fa-box me-2"></i>Ürün</th>
                      <th class="text-center"><i class="fas fa-sort-numeric-up me-2"></i>Adet</th>
                      <th class="text-end"><i class="fas fa-tag me-2"></i>Birim Fiyat</th>
                      <th class="text-end">
                        <i class="fas fa-lira-sign me-2"></i>
                        {{ showPaidTransactions ? "Tutar" : "Kalan Borç" }}
                      </th>
                      <th><i class="fas fa-tags me-2"></i>İşlem Türü</th>
                      <th><i class="fas fa-calendar me-2"></i>Tarih</th>
                      <th class="text-center"><i class="fas fa-info-circle me-2"></i>Durum</th>
                      <th *ngIf="showPaidTransactions" class="text-center">
                        <i class="fas fa-cog me-2"></i>İşlem
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let transaction of member.transactions">
                      <td>
                        <div class="d-flex align-items-center">
                          <i class="fas fa-cube text-muted me-2"></i>
                          {{ transaction.productName || "-" }}
                        </div>
                      </td>
                      <td class="text-center">
                        <span class="modern-badge modern-badge-secondary">
                          {{ transaction.quantity }}
                        </span>
                      </td>
                      <td class="text-end">
                        <strong>{{ transaction.unitPrice | currency:'TRY':'symbol-narrow':'1.2-2' }}</strong>
                      </td>
                      <td class="text-end">
                        <strong [class]="showPaidTransactions ? 'text-success' : 'text-danger'">
                          {{
                            showPaidTransactions
                              ? (transaction.totalPrice | currency:'TRY':'symbol-narrow':'1.2-2')
                              : (transaction.amount | currency:'TRY':'symbol-narrow':'1.2-2')
                          }}
                        </strong>
                      </td>
                      <td>
                        <span class="modern-badge"
                              [ngClass]="{
                                'modern-badge-primary': transaction.transactionType === 'Satış',
                                'modern-badge-success': transaction.transactionType === 'Bakiye Yükleme',
                                'modern-badge-warning': transaction.transactionType === 'İade',
                                'modern-badge-info': transaction.transactionType !== 'Satış' &&
                                                    transaction.transactionType !== 'Bakiye Yükleme' &&
                                                    transaction.transactionType !== 'İade'
                              }">
                          {{ transaction.transactionType }}
                        </span>
                      </td>
                      <td>
                        <small class="text-muted">
                          {{ transaction.transactionDate | date:'dd/MM/yyyy' }}<br>
                          {{ transaction.transactionDate | date:'HH:mm:ss' }}
                        </small>
                      </td>
                      <td class="text-center">
                        <span
                          [class]="getStatusClass(transaction)"
                          (click)="!showPaidTransactions && updatePaymentStatus(transaction)"
                          [style.cursor]="
                            !showPaidTransactions &&
                            transaction.transactionType !== 'Bakiye Yükleme' &&
                            !transaction.isPaid ? 'pointer' : 'default'
                          "
                          [style.pointer-events]="
                            showPaidTransactions ||
                            transaction.transactionType === 'Bakiye Yükleme' ||
                            transaction.isPaid ? 'none' : 'auto'
                          ">
                          {{ getPaymentStatus(transaction) }}
                        </span>
                      </td>
                      <td *ngIf="showPaidTransactions" class="text-center">
                        <button
                          type="button"
                          class="btn btn-sm btn-outline-danger"
                          (click)="deleteTransaction(transaction)"
                          [disabled]="isLoading"
                          title="İşlemi Sil">
                          <i class="fas fa-trash"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
