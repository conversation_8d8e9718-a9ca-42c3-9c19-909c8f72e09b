import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, forkJoin } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { Member } from '../../models/member';
import { MemberService } from '../../services/member.service';
import { MemberEntryService } from '../../services/member-entry.service';
import { ToastrService } from 'ngx-toastr';
import { MemberEntry } from '../../models/memberEntry';

@Component({
  selector: 'app-today-entries',
  templateUrl: './today-entries.component.html',
  styleUrls: ['./today-entries.component.css'],
  standalone: false,
})
export class TodayEntriesComponent implements OnInit {
  entries: MemberEntry[] = [];
  // Will be initialized in constructor
  selectedDate: string;
  isLoading: boolean = false;
  memberControl = new FormControl();
  members: Member[] = [];
  filteredMembers: Observable<Member[]>;
  selectedMember: Member | null = null;
  isSearched: boolean = false;
  initialDate: string;

  constructor(
    private memberService: MemberService,
    private memberEntryService: MemberEntryService,
    private toastrService: ToastrService
  ) {
    // Initialize selectedDate with proper formatting to ensure correct date is sent to backend
    this.selectedDate = this.formatDateForBackend(new Date());
    this.initialDate = this.selectedDate;
  }

  ngOnInit(): void {
    this.isLoading = true;
    console.log('Selected date for API:', this.selectedDate);

    // Tüm verileri paralel olarak yükle
    forkJoin({
      members: this.memberService.getMembers(),
      todayEntries: this.memberEntryService.getTodayEntries(this.selectedDate)
    }).subscribe({
      next: (response) => {
        this.members = response.members.data;
        this.entries = response.todayEntries.data;
        console.log('Received entries:', this.entries.length);
        console.log('Received members:', this.members.length);
        this.isLoading = false;

        // Veriler yüklendikten sonra filtreleme ve subscription'ları başlat
        this.initializeFilteredMembers();
        this.setupMemberControlSubscription();
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this.toastrService.error('Veriler yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  // Format date to ensure correct format for backend
  private formatDateForBackend(date: Date): string {
    // Get local date components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Format as YYYY-MM-DD
    return `${year}-${month}-${day}`;
  }

  private initializeFilteredMembers() {
    this.filteredMembers = this.memberControl.valueChanges.pipe(
      startWith(''),
      map((value) => {
        const name = typeof value === 'string' ? value : value?.name;
        return name ? this._filterMembers(name) : this.members.slice();
      })
    );
  }

  private setupMemberControlSubscription() {
    this.memberControl.valueChanges.subscribe((member) => {
      if (member && typeof member !== 'string') {
        this.selectedMember = member;
      }
    });
  }

  getTotalVisitorsToday(): number {
    const uniqueVisitors = new Set(
      this.entries.map((entry) => entry.phoneNumber)
    );
    return uniqueVisitors.size;
  }

  displayMember(member: Member): string {
    return member ? `${member.name} - ${member.phoneNumber}` : '';
  }

  private _filterMembers(value: string): Member[] {
    const filterValue = value.toLowerCase();
    return this.members.filter(
      (member) =>
        member.name.toLowerCase().includes(filterValue) ||
        member.phoneNumber.includes(filterValue)
    );
  }

  onDateChange() {
    // Tarih değiştiğinde anında API isteği atma, sadece "Ara" butonuna basıldığında arama yap
    console.log('Date changed to:', this.selectedDate);
  }

  shouldShowSearchButton(): boolean {
    // Ara butonu şu durumlarda görünür:
    // 1. Üye arama textbox'ında değer varsa
    // 2. Seçilen tarih başlangıç tarihinden farklıysa
    return !!(this.memberControl.value) || (this.selectedDate !== this.initialDate);
  }

  searchMember() {
    const selectedMember = this.memberControl.value;
    if (selectedMember && (selectedMember.name || selectedMember.phoneNumber)) {
      // İsim filtresi varsa isim bazlı arama yap
      this.isLoading = true;
      this.isSearched = true;
      const searchText = selectedMember.phoneNumber || selectedMember.name;
      this.memberEntryService.getMemberEntriesBySearch(searchText).subscribe({
        next: (response) => {
          this.entries = response.data;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching member entries:', error);
          this.toastrService.error('Üye girişleri yüklenirken bir hata oluştu.', 'Hata');
          this.isLoading = false;
        }
      });
    } else {
      // İsim filtresi yoksa tarih bazlı arama yap
      this.isSearched = false;
      this.getTodayEntries();
    }
  }

  getTodayEntries() {
    this.isLoading = true;
    console.log('Fetching entries for date:', this.selectedDate);
    this.memberEntryService.getTodayEntries(this.selectedDate).subscribe({
      next: (response) => {
        this.entries = response.data;
        console.log('Received entries:', this.entries.length);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching today entries:', error);
        this.toastrService.error(
          'Bugünkü girişler yüklenirken bir hata oluştu.',
          'Hata'
        );
        this.isLoading = false;
      },
    });
  }

  clearSearch() {
    this.memberControl.reset();
    this.selectedMember = null;
    this.isSearched = false;
    // Tarihi de başlangıç tarihine sıfırla
    this.selectedDate = this.initialDate;
    this.getTodayEntries();
  }

  getMembers() {
    this.memberService.getMembers().subscribe({
      next: (response) => {
        this.members = response.data;
      },
      error: (error) => {
        console.error('Error fetching members:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu.', 'Hata');
      },
    });
  }

  calculateDurationInMinutes(entryTime: Date, exitTime: Date): number {
    return Math.floor(
      (new Date(exitTime).getTime() - new Date(entryTime).getTime()) /
        (1000 * 60)
    );
  }

  calculateDuration(entryTime: Date, exitTime: Date | null): string {
    if (!exitTime) {
      return '-';
    }

    const duration = this.calculateDurationInMinutes(entryTime, exitTime);
    if (duration >= 301) {
      return '-';
    }

    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    return hours > 0 ? `${hours} saat ${minutes} dakika` : `${minutes} dakika`;
  }

  isActiveEntry(entry: MemberEntry): boolean {
    return !entry.exitTime;
  }

  shouldShowQRWarning(entry: MemberEntry): boolean {
    if (!entry.exitTime) return false;
    const duration = this.calculateDurationInMinutes(
      entry.entryTime,
      entry.exitTime
    );
    return duration >= 301;
  }
  
  // Get initials from name
  getInitials(name: string): string {
    if (!name) return '';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }
  
  // Generate avatar background color based on name
  getAvatarColor(name: string): string {
    if (!name) return '#4361ee';
    
    const colors = [
      '#4361ee', '#3a0ca3', '#7209b7', '#f72585', 
      '#4cc9f0', '#4895ef', '#560bad', '#b5179e',
      '#3f37c9', '#4361ee', '#4cc9f0', '#00b4d8'
    ];
    
    // Simple hash function to get consistent color for the same name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }
  
  // Get duration class based on duration
  getDurationClass(entry: MemberEntry): string {
    if (!entry.exitTime) return '';
    
    const duration = this.calculateDurationInMinutes(entry.entryTime, entry.exitTime);
    
    if (duration < 30) return 'danger';
    if (duration < 60) return 'warning';
    return 'success';
  }
  
}