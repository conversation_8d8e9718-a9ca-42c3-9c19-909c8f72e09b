<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Summary Cards Row (Günlük, Aylık, Yıllık) -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Daily Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card daily-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-day"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalDailyExpense | number:'1.2-2':'tr' }} ₺</h3>
              <p class="modern-stats-label"><PERSON><PERSON><PERSON><PERSON></p>
            </div>
          </div>
        </div>
        <!-- Monthly Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card monthly-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalMonthlyExpense | number:'1.2-2':'tr' }} ₺</h3>
              <p class="modern-stats-label">Bu Ay Toplam Gider</p>
            </div>
          </div>
        </div>
         <!-- Yearly Total Expense Card -->
         <div class="col-md-4 mb-3">
          <div class="modern-stats-card yearly-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-week"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalYearlyExpense | number:'1.2-2':'tr' }} ₺</h3>
              <p class="modern-stats-label">Bu Yıl Toplam Gider</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Expense Distribution Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">Gider Dağılımı (Türe Göre)</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="expenseDistributionChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">Aylık Gider Trendi</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="monthlyTrendChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content: Table and Filters -->
    <div class="col-md-12">
      <div class="modern-card">
        <!-- Card Header: Title, Actions -->
        <div class="modern-card-header">
          <h5 class="mb-0">Gider Kayıtları</h5>
          <div class="d-flex gap-2 align-items-center flex-wrap">
            <!-- Export Buttons -->
            <button class="modern-btn modern-btn-outline-primary modern-btn-sm"
                    (click)="exportToExcel()"
                    [disabled]="isExporting"
                    title="Excel'e Aktar">
              <i class="fas fa-spinner fa-spin me-1" *ngIf="isExporting"></i>
              <fa-icon [icon]="faFileExcel" class="modern-btn-icon" *ngIf="!isExporting"></fa-icon>
              {{ isExporting ? 'Hazırlanıyor...' : 'Excel' }}
            </button>
            <!-- Add Expense Button -->
            <button class="modern-btn modern-btn-primary modern-btn-sm" (click)="openExpenseDialog()">
              <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon> Yeni Gider Ekle
            </button>
          </div>
        </div>

        <!-- Advanced Filters -->
        <div class="modern-card-body border-bottom">
          <div class="row g-3">
            <!-- Search Filter -->
            <div class="col-md-2">
              <div class="search-input-container">
                <i class="fas fa-search search-icon" *ngIf="!isSearching"></i>
                <i class="fas fa-spinner fa-spin search-icon" *ngIf="isSearching"></i>
                <input
                  type="text"
                  class="search-input modern-form-control modern-form-control-sm"
                  placeholder="Tür veya açıklama ara..."
                  [formControl]="searchControl" />
              </div>
            </div>

            <!-- Date Range Filter -->
            <div class="col-md-2">
              <input
                type="date"
                class="modern-form-control modern-form-control-sm"
                placeholder="Başlangıç Tarihi"
                [(ngModel)]="filterState.startDate" />
            </div>
            <div class="col-md-2">
              <input
                type="date"
                class="modern-form-control modern-form-control-sm"
                placeholder="Bitiş Tarihi"
                [(ngModel)]="filterState.endDate" />
            </div>

            <!-- Expense Type Filter -->
            <div class="col-md-2">
              <select
                class="modern-form-control modern-form-control-sm"
                [(ngModel)]="filterState.expenseType">
                <option value="">Tüm Türler</option>
                <option *ngFor="let type of expenseTypes" [value]="type">{{ type }}</option>
              </select>
            </div>

            <!-- Amount Range Filter -->
            <div class="col-md-1">
              <input
                type="number"
                class="modern-form-control modern-form-control-sm"
                placeholder="Min ₺"
                [(ngModel)]="filterState.minAmount" />
            </div>
            <div class="col-md-1">
              <input
                type="number"
                class="modern-form-control modern-form-control-sm"
                placeholder="Max ₺"
                [(ngModel)]="filterState.maxAmount" />
            </div>

            <!-- Filter Actions -->
            <div class="col-md-1">
              <div class="d-flex gap-1">
                <button class="modern-btn modern-btn-primary"
                        (click)="applyAdvancedFilters()"
                        [disabled]="isSearching"
                        title="Filtreleri Uygula">
                  <i class="fas fa-spinner fa-spin" *ngIf="isSearching"></i>
                  <i class="fas fa-search" *ngIf="!isSearching"></i>
                  {{ isSearching ? 'Aranıyor...' : 'Ara' }}
                </button>
                <button class="modern-btn modern-btn-secondary"
                        (click)="clearFilters()"
                        [disabled]="!hasActiveFilters() || isSearching"
                        title="Filtreleri Temizle">
                         KABLOSUZ
                  <i class="fas fa-filter-circle-xmark"></i>
                </button>

              </div>
            </div>
          </div>

          <!-- Favori filtreler kaldırıldı -->

          <!-- Favori filtreler kaldırıldı -->

          <!-- Dashboard filters kaldırıldı -->
        </div>

        <!-- Card Body: Table and Pagination -->
        <div class="modern-card-body">
          <!-- Table Info and Page Size -->
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="table-info">
              <span class="text-muted">
                Toplam {{ paginatedExpenses.totalCount }} kayıttan
                {{ (paginatedExpenses.pageNumber - 1) * paginatedExpenses.pageSize + 1 }}-{{
                  Math.min(paginatedExpenses.pageNumber * paginatedExpenses.pageSize, paginatedExpenses.totalCount)
                }} arası gösteriliyor
              </span>
            </div>
            <div class="page-size-selector">
              <label class="form-label me-2 mb-0">Sayfa başına:</label>
              <select class="modern-form-control modern-form-control-sm"
                      [(ngModel)]="paginatedExpenses.pageSize"
                      (ngModelChange)="changePageSize($event)"
                      style="width: auto; display: inline-block;">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
            </div>
          </div>

          <!-- Expense Table -->
          <div class="table-responsive" *ngIf="!isLoading && paginatedExpenses.data.length > 0">
            <table class="modern-table">
              <thead>
                <tr>
                  <th class="text-center sortable" (click)="sortBy('ExpenseType')">
                    Tür
                    <fa-icon [icon]="getSortIcon('ExpenseType')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center sortable" (click)="sortBy('Amount')">
                    Tutar
                    <fa-icon [icon]="getSortIcon('Amount')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center sortable" (click)="sortBy('ExpenseDate')">
                    Gider Tarihi
                    <fa-icon [icon]="getSortIcon('ExpenseDate')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center sortable" (click)="sortBy('Description')">
                    Açıklama
                    <fa-icon [icon]="getSortIcon('Description')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let expense of paginatedExpenses.data; let i = index"
                    [style.animation-delay]="i * 0.05 + 's'"
                    class="staggered-item">
                  <td class="text-center" data-label="Tür">
                    <div class="expense-type-cell" *ngIf="expense.expenseType; else noType">
                      <div class="expense-type-icon" [title]="getExpenseTypeTooltip(expense.expenseType)">
                        <i [class]="getExpenseTypeIcon(expense.expenseType)"></i>
                      </div>
                      <span class="modern-badge" [ngClass]="getBadgeClass(expense.expenseType)">
                        {{ expense.expenseType }}
                      </span>
                    </div>
                    <ng-template #noType>
                      <div class="expense-type-cell">
                        <div class="expense-type-icon" title="Gider türü belirtilmemiş">
                          <i class="fas fa-question-circle text-muted"></i>
                        </div>
                        <span class="text-muted">-</span>
                      </div>
                    </ng-template>
                  </td>
                  <td class="text-center" data-label="Tutar">
                    <div class="amount-cell" [title]="'Tutar: ' + (expense.amount | number:'1.2-2':'tr') + ' ₺'">
                      <span class="amount-value">{{ expense.amount | number:'1.2-2':'tr' }} ₺</span>
                    </div>
                  </td>
                  <td class="text-center" data-label="Gider Tarihi">
                    <div class="date-cell" [title]="'Gider Tarihi: ' + (expense.expenseDate | date:'dd MMMM yyyy, EEEE':'tr')">
                      <i class="fas fa-calendar-alt date-icon"></i>
                      <span class="date-value">{{ expense.expenseDate | date:'dd/MM/yyyy' }}</span>
                    </div>
                  </td>
                  <td class="text-center" data-label="Açıklama">
                    <div class="description-cell" *ngIf="expense.description; else noDescription"
                         [title]="expense.description">
                      <i class="fas fa-comment-alt description-icon"></i>
                      <span class="description-value">{{
                        expense.description.length > 30 ?
                        (expense.description | slice:0:30) + '...' :
                        expense.description
                      }}</span>
                    </div>
                    <ng-template #noDescription>
                      <div class="description-cell">
                        <i class="fas fa-comment-slash text-muted description-icon"></i>
                        <span class="text-muted">Açıklama yok</span>
                      </div>
                    </ng-template>
                  </td>
                  <td data-label="İşlemler">
                    <div class="d-flex justify-content-center gap-2 action-buttons">
                      <button
                        class="modern-btn modern-btn-primary modern-btn-sm modern-btn-icon-only action-btn"
                        [title]="'Düzenle: ' + (expense.expenseType || 'Gider')"
                        (click)="openExpenseDialog(expense)"
                        [attr.aria-label]="'Gideri düzenle'">
                        <fa-icon [icon]="faEdit"></fa-icon>
                      </button>
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm modern-btn-icon-only action-btn"
                        [title]="'Sil: ' + (expense.expenseType || 'Gider') + ' - ' + (expense.amount | number:'1.2-2':'tr') + ' ₺'"
                        (click)="deleteExpense(expense)"
                        [attr.aria-label]="'Gideri sil'">
                        <fa-icon [icon]="faTrashAlt"></fa-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="pagination-container mt-4" *ngIf="!isLoading && paginatedExpenses.totalPages > 1">
            <nav aria-label="Sayfa navigasyonu">
              <ul class="pagination justify-content-center">
                <li class="page-item" [class.disabled]="!paginatedExpenses.hasPrevious">
                  <button class="page-link" (click)="goToPage(1)" [disabled]="!paginatedExpenses.hasPrevious">
                    <i class="fas fa-angle-double-left"></i>
                  </button>
                </li>
                <li class="page-item" [class.disabled]="!paginatedExpenses.hasPrevious">
                  <button class="page-link" (click)="goToPage(paginatedExpenses.pageNumber - 1)" [disabled]="!paginatedExpenses.hasPrevious">
                    <i class="fas fa-angle-left"></i>
                  </button>
                </li>

                <li class="page-item"
                    *ngFor="let page of getPageNumbers()"
                    [class.active]="page === paginatedExpenses.pageNumber">
                  <button class="page-link" (click)="goToPage(page)">{{ page }}</button>
                </li>

                <li class="page-item" [class.disabled]="!paginatedExpenses.hasNext">
                  <button class="page-link" (click)="goToPage(paginatedExpenses.pageNumber + 1)" [disabled]="!paginatedExpenses.hasNext">
                    <i class="fas fa-angle-right"></i>
                  </button>
                </li>
                <li class="page-item" [class.disabled]="!paginatedExpenses.hasNext">
                  <button class="page-link" (click)="goToPage(paginatedExpenses.totalPages)" [disabled]="!paginatedExpenses.hasNext">
                    <i class="fas fa-angle-double-right"></i>
                  </button>
                </li>
              </ul>
            </nav>
          </div>

          <!-- No Results Message (Empty State) -->
          <div class="empty-state" *ngIf="!isLoading && paginatedExpenses.data.length === 0">
            <i class="fas fa-ghost fa-3x text-muted mb-3"></i>
            <h5 class="mt-3">Gider Bulunamadı</h5>
            <p class="text-muted">Seçili kriterlere uygun gider kaydı bulunamadı.</p>
            <button class="modern-btn modern-btn-primary mt-3" (click)="openExpenseDialog()">
              <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon> İlk Gideri Ekle
            </button>
          </div>

        </div> <!-- End Card Body -->
      </div> <!-- End Modern Card -->
    </div> <!-- End Col -->
  </div> <!-- End Row -->
</div> <!-- End Container -->